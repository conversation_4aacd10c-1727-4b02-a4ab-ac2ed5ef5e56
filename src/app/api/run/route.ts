import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";
import axios from "axios";
import { ErrorMessage } from "@/app/utils/errorMessage";

export const POST = withApiAuthRequired(async function POST(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: { params?: Record<string, string | string[]> } // Required context type
) {
  try {
    console.log("🏃 Run API: Starting run data retrieval");

    // Get access token with proper response handling
    const { accessToken } = await getAccessToken(req, new NextResponse());
    console.log(
      "🔑 Run API: Access token retrieved:",
      accessToken ? "✅ Present" : "❌ Missing"
    );

    // Parse request body
    const body = await req.json();
    console.log("📤 Run API: Request body:", body);

    if (!body.runID) {
      console.error("❌ Run API: runID is missing from request body");
      return NextResponse.json({ error: "runID is required" }, { status: 400 });
    }

    const apiUrl = `${process.env.NEXT_PUBLIC_BACKEND_ENDPOINT}/api/v1/runs/${body.runID}`;
    console.log("🌐 Run API: Making request to:", apiUrl);

    // Make API call with improved error handling
    const { data } = await axios.get(apiUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json",
      },
      timeout: 10_000, // 10 seconds (using numeric separator)
      validateStatus: () => true,
    });

    console.log("📊 Run API: Response data structure:", {
      hasData: !!data,
      hasRunDetails: !!data?.run_details,
      runDetailsKeys: data?.run_details ? Object.keys(data.run_details) : [],
    });

    return NextResponse.json(data);
  } catch (error: unknown) {
    console.error(
      "❌ Run API Error:",
      error instanceof Error ? error.message : error
    );

    // Handle different error types
    if (axios.isAxiosError(error)) {
      console.error("❌ Axios error details:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
      });

      return NextResponse.json(
        {
          error: ErrorMessage.runData,
          details: error.response?.data || error.message,
        },
        { status: error.response?.status || 500 }
      );
    }

    return NextResponse.json({ error: ErrorMessage.runData }, { status: 500 });
  }
}) as any; // Temporary Auth0 compatibility fix
