import { NextRequest, NextResponse } from "next/server";
import { getAccessToken, withApiAuthRequired } from "@auth0/nextjs-auth0";

export const GET = withApiAuthRequired(async function GET(
  req: NextRequest,
  // eslint-disable-next-line no-unused-vars
  context: { params?: Record<string, string | string[]> } // Required context
) {
  try {
    console.log("🔑 Token API: Starting token retrieval");

    const { accessToken } = await getAccessToken(req, new NextResponse());

    console.log(
      "🔑 Token API: Access token retrieved:",
      accessToken ? "✅ Present" : "❌ Missing"
    );
    console.log("🔑 Token API: Token length:", accessToken?.length || 0);

    return NextResponse.json({ accessToken });
  } catch (error: unknown) {
    console.error("❌ Token API Error:", error);
    return NextResponse.json(
      { error: "Failed to retrieve access token" },
      { status: 500 }
    );
  }
}) as any; // Temporary Auth0 fix
