"use client";
import React from "react";
import { DetailedRun } from "@/app/_components/_insights/types";
import { withPageAuthRequired } from "@auth0/nextjs-auth0/client";
import { useEffect, useState } from "react";
import * as Sentry from "@sentry/react";
import { ErrorMessage } from "@/app/utils/errorMessage";

interface InsightsPageProps {
  params: { slug: string };
}

const runFetcher = async (uri: string, runID: string) => {
  console.log("🌐 Starting runFetcher with:", { uri, runID });

  const body = JSON.stringify({ runID: runID });
  console.log("📤 Request body:", body);

  const response = await fetch(uri, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: body,
    cache: "no-cache",
  });

  console.log("📥 Response status:", response.status);
  console.log("📥 Response ok:", response.ok);

  if (!response.ok) {
    const errorText = await response.text();
    console.error("❌ Response error:", errorText);

    const error = new Error(`Failed to fetch run data: ${response.statusText}`);
    Sentry.captureException(error, {
      extra: { uri, runID, status: response.status, errorText },
    });
    throw error;
  }

  try {
    const data = await response.json();
    console.log("✅ Successfully parsed response data");
    return data;
  } catch (error) {
    console.error("❌ Failed to parse JSON response:", error);
    Sentry.captureException(error, { extra: { uri, runID } });
    throw error;
  }
};

const InsightsPage = ({ params }: InsightsPageProps) => {
  const [run, setRun] = useState<DetailedRun | undefined>(undefined);
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const [runName, setRunName] = useState("");
  const [runID, setRunID] = useState("");
  const [analyticsURL, setAnalyticsURL] = useState(
    process.env.NEXT_PUBLIC_ANALYTICS_URL
  );

  const iframeId = "insights-iframe";

  useEffect(() => {
    console.log("🔄 Setting up iframe load listener");

    const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
    console.log("🖼️ Iframe element found:", !!iframe);

    if (!iframe) {
      console.error("❌ Iframe element not found!");
      return;
    }

    const handleIframeLoad = () => {
      console.log("🖼️ Iframe loaded successfully");
      setIframeLoaded(true);
      getRunData();
    };

    iframe.addEventListener("load", handleIframeLoad);
    console.log("✅ Iframe load listener added");

    // Check if iframe content is already loaded by trying to access contentWindow
    try {
      if (
        iframe.contentWindow &&
        iframe.contentWindow.location.href !== "about:blank"
      ) {
        console.log("🖼️ Iframe already loaded, calling getRunData immediately");
        handleIframeLoad();
      }
    } catch (e) {
      // Cross-origin access might be blocked, which is normal
      console.log(
        "🖼️ Iframe content not accessible yet (normal for cross-origin)"
      );
    }

    return () => {
      console.log("🧹 Cleaning up iframe load listener");
      iframe.removeEventListener("load", handleIframeLoad);
    };
  }, []);

  const getRunData = async () => {
    try {
      console.log("🔍 Starting getRunData function");
      console.log("📋 Params slug:", params.slug);

      const fetchRun = await runFetcher("/api/run", params.slug);
      console.log("📊 Fetched run data:", fetchRun);

      setRun(fetchRun);

      if (fetchRun) {
        console.log("📊 Run details structure:", fetchRun.run_details);

        const runId = fetchRun.run_details?.run_id;
        const runName = fetchRun.run_details?.run_name;

        console.log("🆔 Extracted run ID:", runId);
        console.log("📝 Extracted run name:", runName);

        setRunID(runId || "");
        setRunName(runName || "");

        console.log("✅ State updated with run data");
      } else {
        console.error("❌ Run data is undefined");
        throw new Error("Run data is undefined");
      }
    } catch (error) {
      console.error("❌ Error in getRunData:", error);
      Sentry.captureException(new Error(ErrorMessage.runData), {
        extra: {
          slug: params.slug,
          error: error instanceof Error ? error.message : error,
        },
      });
    }
  };

  const sendRunNameToShiny = async () => {
    try {
      console.log("🔍 Starting sendRunNameToShiny function");
      console.log("📊 Current state values:", {
        runName,
        runID,
        analyticsURL,
        iframeLoaded,
      });

      const response = await fetch("/api/token");
      console.log("🔑 Token response status:", response.status);

      if (!response.ok) {
        throw new Error(`Token fetch failed with status: ${response.status}`);
      }

      const data = await response?.json();
      console.log("🔑 Token response data:", data);

      const accessToken = data?.accessToken;
      console.log(
        "🔑 Extracted accessToken:",
        accessToken ? "✅ Present" : "❌ Missing"
      );

      const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
      console.log("🖼️ Iframe element:", iframe ? "✅ Found" : "❌ Not found");

      const apiURL = process.env.NEXT_PUBLIC_BACKEND_ENDPOINT;
      console.log("🌐 API URL from env:", apiURL);

      const message = {
        runName,
        accessToken,
        apiURL,
        runID,
      };

      console.log("📤 Message being sent to Shiny:", message);
      console.log("📤 Message properties check:", {
        hasRunName: !!message.runName,
        hasAccessToken: !!message.accessToken,
        hasApiURL: !!message.apiURL,
        hasRunID: !!message.runID,
      });

      if (!iframe.contentWindow) {
        throw new Error("Iframe contentWindow is not available");
      }

      iframe.contentWindow.postMessage(message, analyticsURL!);
      console.log("✅ Message sent successfully to:", analyticsURL);
    } catch (error) {
      console.error("❌ Error in sendRunNameToShiny:", error);
      Sentry.captureException(new Error("Failed to send run name to Shiny"), {
        extra: {
          runName,
          runID,
          error: error instanceof Error ? error.message : error,
        },
      });
    }
  };

  useEffect(() => {
    console.log("🔄 useEffect triggered with:", { iframeLoaded, runName });

    if (iframeLoaded && runName) {
      console.log("✅ Starting Shiny communication setup");

      const checkShinyReady = setInterval(() => {
        console.log("🔄 Checking if Shiny is ready...");
        const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
        if (iframe && iframe.contentWindow) {
          iframe.contentWindow.postMessage({ type: "isShinyReady" }, "*");
        } else {
          console.warn("⚠️ Iframe or contentWindow not available");
        }
      }, 1000);

      const handleMessage = (event: MessageEvent) => {
        console.log("📨 Received message from iframe:", event.data);
        console.log("📨 Message origin:", event.origin);

        if (event.data && event.data.type === "shinyReady") {
          console.log("✅ Shiny is ready! Sending run data...");
          sendRunNameToShiny();
          clearInterval(checkShinyReady);
        } else if (event.data && event.data.type === "error") {
          console.error("❌ Error from Shiny:", event.data);
        }
      };

      window.addEventListener("message", handleMessage);

      return () => {
        console.log("🧹 Cleaning up Shiny communication");
        window.removeEventListener("message", handleMessage);
        clearInterval(checkShinyReady);
      };
    } else {
      console.log("⏳ Waiting for iframe to load and run data to be available");
    }
  }, [iframeLoaded, runName]);

  return (
    <div className="h-full w-full">
      <iframe
        id={iframeId}
        src={analyticsURL}
        title="Insights"
        className="w-full h-full"
      />
    </div>
  );
};

export default withPageAuthRequired(
  Sentry.withProfiler(InsightsPage as React.ComponentType<unknown>)
) as React.ComponentType;
